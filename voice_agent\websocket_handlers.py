import asyncio
import json
import logging
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List
from .websocket_manager import WebSocketConnection
from .call_context import CallContext
from .language_config import language_config, get_primary_language, get_supported_languages
from .language_utils import (
    contains_indian_script, detect_language_async, get_hospital_preferred_languages,
    set_hospital_language_preferences, get_supported_languages as get_utils_supported_languages
)
from .semantic_integration import process_query, set_hospital_language_preferences as set_semantic_preferences
from .llm_integration import process_voice_input, extract_booking_intent
from .database import get_doctors, get_tests
from .websocket_state_handlers import state_handlers

# Module-local logger
logger = logging.getLogger(__name__)

def extract_hospital_id_from_path(path: str) -> Optional[str]:
    """
    Extract hospital ID from WebSocket path
    Expected format: /ws/jambonz/{hospital_id}
    
    Args:
        path: WebSocket connection path
        
    Returns:
        Hospital ID or None if not found
    """
    try:
        parts = path.strip('/').split('/')
        if len(parts) >= 3 and parts[0] == 'ws' and parts[1] == 'jambonz':
            return parts[2]
    except Exception as e:
        logger.error(f"Error extracting hospital ID from path {path}: {e}")
    
    return None

class JambonzMessageHandler:
    """
    Handles different types of messages from Jambonz WebSocket API.
    Processes session management, verb hooks, and call status updates.
    """
    
    def __init__(self):
        """Initialize message handler"""
        self.active_sessions: Dict[str, Dict[str, Any]] = {}  # call_sid -> session_data
    
    async def handle_session_new(self, connection: WebSocketConnection, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle session:new message - equivalent to initial webhook call
        
        Args:
            connection: WebSocket connection
            data: Message data from Jambonz
            
        Returns:
            Acknowledgment response with initial verbs
        """
        try:
            call_sid = data.get("call_sid") or data.get("callSid")
            caller_number = data.get("from")
            called_number = data.get("to")
            
            if not call_sid:
                call_sid = str(uuid.uuid4())
                logger.warning(f"No call_sid provided, generated: {call_sid}")
            
            # Extract hospital ID from called number or connection path
            hospital_id = self._extract_hospital_id(data, connection)
            if not hospital_id:
                logger.error(f"Unable to determine hospital ID for call {call_sid}")
                return self._create_error_response("Unable to determine hospital")
            
            # Update connection metadata
            connection.call_sid = call_sid
            connection.hospital_id = hospital_id
            connection.metrics.call_sid = call_sid
            connection.metrics.hospital_id = hospital_id
            
            # Initialize call context
            ctx = await CallContext.get_or_create(
                call_id=call_sid,
                hospital_id=hospital_id,
                caller_number=caller_number
            )
            
            # Store session data
            self.active_sessions[call_sid] = {
                "connection_id": connection.connection_id,
                "hospital_id": hospital_id,
                "caller_number": caller_number,
                "called_number": called_number,
                "created_at": datetime.now().isoformat()
            }
            
            # Get hospital configuration
            from .main import get_hospital_config
            try:
                hospital_config = await get_hospital_config(hospital_id)
            except Exception as e:
                logger.error(f"Error getting hospital config for {hospital_id}: {e}")
                return self._create_error_response("Hospital configuration not found")
            
            # Generate welcome message with enhanced language support
            hospital_languages = getattr(hospital_config, 'languages', None)
            if not hospital_languages:
                hospital_languages = get_utils_supported_languages()

            # Set hospital language preferences in the language utilities
            set_hospital_language_preferences(hospital_id, hospital_languages)

            # Also set in semantic integration for consistency
            await set_semantic_preferences(hospital_id, hospital_languages)

            # Ensure Hindi is first if available (primary language preference)
            if "hi" in hospital_languages and hospital_languages[0] != "hi":
                hospital_languages = ["hi"] + [lang for lang in hospital_languages if lang != "hi"]
            
            # Create language selection options
            language_options = ""
            for idx, lang_code in enumerate(hospital_languages, 1):
                lang_name = language_config.get_language_name(lang_code, "hi")
                if idx == 1:
                    language_options += f", {lang_name} के लिए {idx} दबाएं"
                else:
                    language_options += f", {lang_name} के लिए {idx} दबाएं"
            
            welcome_message = language_config.get_welcome_message(
                "hi",
                hospital_config.name,
                language_options
            )
            
            # Create initial response with gather verb
            response = {
                "type": "ack",
                "verbs": [{
                    "verb": "gather",
                    "actionHook": "websocket",  # WebSocket-only mode - no HTTP hooks
                    "input": ["speech", "dtmf"],
                    "timeout": 10,
                    "bargein": True,
                    "say": {
                        "text": welcome_message
                    }
                }]
            }
            
            logger.info(f"Session started for call {call_sid}, hospital {hospital_id}")
            return response
            
        except Exception as e:
            logger.error(f"Error handling session:new: {e}")
            return self._create_error_response("Session initialization failed")
    
    async def handle_verb_hook(self, connection: WebSocketConnection, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle verb:hook message - equivalent to webhook gather/action hooks
        
        Args:
            connection: WebSocket connection
            data: Message data from Jambonz
            
        Returns:
            Acknowledgment response with next verbs
        """
        try:
            call_sid = data.get("call_sid") or data.get("callSid")
            if not call_sid:
                logger.error("No call_sid in verb:hook message")
                return self._create_error_response("Missing call_sid")
            
            # Get call context
            ctx = await CallContext.get_or_create(call_id=call_sid)
            if not ctx:
                logger.error(f"No context found for call {call_sid}")
                return self._create_error_response("Call context not found")
            
            # Extract user input
            speech_result = ""
            dtmf_digits = ""
            
            # Handle different input types
            if "speech" in data:
                speech_data = data["speech"]
                if isinstance(speech_data, dict):
                    alternatives = speech_data.get("alternatives", [])
                    if alternatives and len(alternatives) > 0:
                        speech_result = alternatives[0].get("transcript", "")
                elif isinstance(speech_data, str):
                    speech_result = speech_data
            
            if "dtmf" in data:
                dtmf_data = data["dtmf"]
                if isinstance(dtmf_data, dict):
                    dtmf_digits = dtmf_data.get("digits", "")
                elif isinstance(dtmf_data, str):
                    dtmf_digits = dtmf_data
            
            # Process the input using existing logic from main.py
            response_verbs = await self._process_user_input(
                ctx, speech_result, dtmf_digits, connection.hospital_id
            )
            
            return {
                "type": "ack",
                "verbs": response_verbs
            }
            
        except Exception as e:
            logger.error(f"Error handling verb:hook: {e}")
            return self._create_error_response("Input processing failed")
    
    async def handle_call_status(self, connection: WebSocketConnection, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Handle call:status message - call state changes
        
        Args:
            connection: WebSocket connection
            data: Message data from Jambonz
            
        Returns:
            Optional response (usually None for status messages)
        """
        try:
            call_sid = data.get("call_sid") or data.get("callSid")
            call_status = data.get("call_status") or data.get("callStatus")
            
            if call_sid:
                logger.info(f"Call status update: {call_sid} -> {call_status}")
                
                # Clean up session data if call ended
                if call_status in ["completed", "failed", "busy", "no-answer"]:
                    if call_sid in self.active_sessions:
                        del self.active_sessions[call_sid]
                    
                    # Clear call context
                    try:
                        ctx = await CallContext.get_or_create(call_id=call_sid)
                        if ctx:
                            await ctx.clear()
                    except Exception as e:
                        logger.error(f"Error clearing context for call {call_sid}: {e}")
            
            # Status messages typically don't require responses
            return None
            
        except Exception as e:
            logger.error(f"Error handling call:status: {e}")
            return None
    
    async def handle_session_reconnect(self, connection: WebSocketConnection, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle session:reconnect message - WebSocket reconnection
        
        Args:
            connection: WebSocket connection
            data: Message data from Jambonz
            
        Returns:
            Acknowledgment response
        """
        try:
            call_sid = data.get("call_sid") or data.get("callSid")
            
            if call_sid and call_sid in self.active_sessions:
                # Restore connection metadata
                session_data = self.active_sessions[call_sid]
                connection.call_sid = call_sid
                connection.hospital_id = session_data.get("hospital_id")
                connection.metrics.call_sid = call_sid
                connection.metrics.hospital_id = session_data.get("hospital_id")
                connection.metrics.reconnection_count += 1
                
                logger.info(f"Session reconnected for call {call_sid}")
                
                # Get current call context to determine appropriate response
                ctx = await CallContext.get_or_create(call_id=call_sid)
                if ctx:
                    # Continue from current state
                    response_verbs = await self._get_state_appropriate_verbs(ctx)
                    return {
                        "type": "ack",
                        "verbs": response_verbs
                    }
            
            logger.warning(f"Reconnection for unknown call: {call_sid}")
            return self._create_error_response("Unknown call session")
            
        except Exception as e:
            logger.error(f"Error handling session:reconnect: {e}")
            return self._create_error_response("Reconnection failed")
    
    def _extract_hospital_id(self, data: Dict[str, Any], connection: WebSocketConnection) -> Optional[str]:
        """Extract hospital ID from call data or connection"""
        # PRIORITY 1: Use hospital ID from WebSocket connection (extracted from path)
        if connection and connection.hospital_id:
            logger.info(f"Using hospital ID from WebSocket connection: {connection.hospital_id}")
            return connection.hospital_id

        # PRIORITY 2: Try to extract from called number (DID)
        called_number = data.get("to")
        if called_number:
            # Use existing logic from hospital_utils
            from .hospital_utils import extract_hospital_id_from_did
            hospital_id = extract_hospital_id_from_did(called_number)
            if hospital_id:
                logger.info(f"Extracted hospital ID from DID {called_number}: {hospital_id}")
                return hospital_id

        # PRIORITY 3: Try to extract from caller number (from) as fallback
        caller_number = data.get("from")
        if caller_number:
            from .hospital_utils import extract_hospital_id_from_did
            hospital_id = extract_hospital_id_from_did(caller_number)
            if hospital_id:
                logger.info(f"Extracted hospital ID from caller number {caller_number}: {hospital_id}")
                return hospital_id

        # LAST RESORT: Log error and use environment variable or default
        logger.error(f"Could not extract hospital ID from connection path, called number ({called_number}), or caller number ({caller_number}). Using fallback.")
        import os
        fallback_id = os.environ.get("DEFAULT_HOSPITAL_ID", "1")
        logger.warning(f"Using fallback hospital ID: {fallback_id}")
        return fallback_id
    
    def _create_error_response(self, error_message: str) -> Dict[str, Any]:
        """Create error response for Jambonz"""
        return {
            "type": "ack",
            "verbs": [{
                "verb": "say",
                "text": f"Sorry, {error_message}. Please try your call again later."
            }, {
                "verb": "hangup"
            }]
        }

    async def _process_user_input(self, ctx: CallContext, speech_result: str,
                                 dtmf_digits: str, hospital_id: str) -> List[Dict[str, Any]]:
        """
        Process user input and return appropriate verbs
        This mirrors the logic from main.py handle_gather function

        Args:
            ctx: Call context
            speech_result: Speech recognition result
            dtmf_digits: DTMF digits pressed
            hospital_id: Hospital ID

        Returns:
            List of Jambonz verbs
        """
        try:
            # Handle emergency case (0 pressed)
            if dtmf_digits == '0':
                logger.info(f"Call ID {ctx.call_id}: Emergency option 0 selected.")
                return [{
                    "verb": "say",
                    "text": "Connecting you to emergency services. Please stay on the line."
                }, {
                    "verb": "hangup"
                }]

            # Enhanced LLM processing for speech input with language detection
            if speech_result and len(speech_result.strip()) > 3:
                try:
                    # Enhanced language detection for better processing
                    language_info = await detect_language_async(speech_result, hospital_id)
                    detected_language = language_info.get('primary_language') or ctx.language or get_primary_language()

                    # Update context language if detection is confident
                    if (language_info.get('confidence_scores', {}).get(language_info.get('script', ''), 0) > 0.7
                        and detected_language != ctx.language):
                        await ctx.set_language(detected_language)
                        logger.info(f"Updated language to {detected_language} based on speech detection")

                    # First try semantic cache for quick responses
                    semantic_result = await process_query(
                        query=speech_result,
                        hospital_id=hospital_id,
                        language=detected_language,
                        context={"state": ctx.state, "caller_number": ctx.caller_number, "language_info": language_info}
                    )

                    # If we got a high-confidence semantic response, use it
                    if semantic_result["confidence"] > 0.8:
                        # Log additional information about date-aware processing
                        source_info = semantic_result.get("source", "unknown")
                        date_aware = semantic_result.get("date_aware", False)
                        date_context = semantic_result.get("date_context")
                        availability_status = semantic_result.get("availability_status", {})

                        log_msg = f"Semantic response for query: '{speech_result}' (confidence: {semantic_result['confidence']:.2f}, source: {source_info}"
                        if date_aware:
                            log_msg += f", date-aware: True"
                            if date_context and date_context.get("relative_reference"):
                                log_msg += f", date: {date_context['relative_reference']}"
                            if availability_status.get("available") is not None:
                                log_msg += f", available: {availability_status['available']}"
                        log_msg += ")"

                        logger.info(log_msg)

                        return [{
                            "verb": "say",
                            "text": semantic_result["response"]
                        }, {
                            "verb": "gather",
                            "actionHook": "websocket",
                            "input": ["speech", "dtmf"],
                            "timeout": 10,
                            "bargein": True,
                            "say": {
                                "text": "Is there anything else I can help you with?"
                            }
                        }]

                    # If semantic cache didn't provide good response, try LLM processing
                    if semantic_result["confidence"] < 0.8:
                        llm_context = {
                            "hospital_id": hospital_id,
                            "language": ctx.language or get_primary_language(),
                            "hospital_name": f'Hospital {hospital_id}',  # Could be enhanced
                            "user_info": {
                                "phone": ctx.caller_number,
                                "call_id": ctx.call_id
                            },
                            "state": ctx.state,
                            "chat_history": []
                        }

                        # Process with LLM for intelligent understanding
                        llm_result = await process_voice_input(speech_result, llm_context)

                        if llm_result.get("success") and llm_result.get("response"):
                            logger.info(f"LLM processing successful for query: '{speech_result}'")

                            return [{
                                "verb": "say",
                                "text": llm_result["response"]
                            }, {
                                "verb": "gather",
                                "actionHook": "websocket",
                                "input": ["speech", "dtmf"],
                                "timeout": 10,
                                "bargein": True,
                                "say": {
                                    "text": "Is there anything else I can help you with?"
                                }
                            }]

                except Exception as e:
                    logger.error(f"Error in LLM processing: {e}")
                    # Continue with state machine flow

            # State machine processing (mirrors main.py logic)
            return await self._process_state_machine(ctx, speech_result, dtmf_digits, hospital_id)

        except Exception as e:
            logger.error(f"Error processing user input: {e}")
            return [{
                "verb": "say",
                "text": "Sorry, I encountered an error processing your request. Please try again."
            }, {
                "verb": "hangup"
            }]

    async def _process_state_machine(self, ctx: CallContext, speech_result: str,
                                   dtmf_digits: str, hospital_id: str) -> List[Dict[str, Any]]:
        """
        Process state machine logic (mirrors main.py handle_gather)

        Args:
            ctx: Call context
            speech_result: Speech input
            dtmf_digits: DTMF input
            hospital_id: Hospital ID

        Returns:
            List of Jambonz verbs
        """
        state = ctx.state

        if state == "greeting":
            # Language selection logic
            return await state_handlers.handle_language_selection(ctx, speech_result, dtmf_digits, hospital_id)

        elif state == "main_menu":
            # Main menu selection
            return await state_handlers.handle_main_menu(ctx, speech_result, dtmf_digits, hospital_id)

        elif state == "doctor_booking":
            # Doctor selection
            return await state_handlers.handle_doctor_booking(ctx, speech_result, dtmf_digits, hospital_id)

        elif state == "test_booking":
            # Test selection
            return await state_handlers.handle_test_booking(ctx, speech_result, dtmf_digits, hospital_id)

        elif state in ["appointment_time", "test_time"]:
            # Time selection
            return await state_handlers.handle_time_selection(ctx, speech_result, dtmf_digits, hospital_id)

        elif state == "patient_name":
            # Patient name collection
            return await state_handlers.handle_patient_name(ctx, speech_result, dtmf_digits, hospital_id)

        elif state == "confirmation":
            # Booking confirmation
            return await state_handlers.handle_confirmation(ctx, speech_result, dtmf_digits, hospital_id)

        else:
            # Unknown state
            logger.warning(f"Unknown state: {state} for call {ctx.call_id}")
            return [{
                "verb": "say",
                "text": "I'm sorry, something went wrong. Let me transfer you to our main menu."
            }, {
                "verb": "gather",
                "actionHook": "websocket",
                "input": ["speech", "dtmf"],
                "timeout": 10,
                "bargein": True,
                "say": {
                    "text": "Please say 1 for doctor appointment or 2 for medical test."
                }
            }]

    async def _get_state_appropriate_verbs(self, ctx: CallContext) -> List[Dict[str, Any]]:
        """
        Get appropriate verbs for current call state (for reconnections)

        Args:
            ctx: Call context

        Returns:
            List of appropriate verbs
        """
        state = ctx.state
        language = ctx.language or get_primary_language()

        if state == "greeting":
            return [{
                "verb": "gather",
                "actionHook": "websocket",
                "input": ["speech", "dtmf"],
                "timeout": 10,
                "bargein": True,
                "say": {
                    "text": "Please select your language. हिंदी के लिए 1 दबाएं, English के लिए 2 दबाएं।"
                }
            }]

        elif state == "main_menu":
            return [{
                "verb": "gather",
                "actionHook": "websocket",
                "input": ["speech", "dtmf"],
                "timeout": 10,
                "bargein": True,
                "say": {
                    "text": "Please say 1 for doctor appointment or 2 for medical test."
                }
            }]

        else:
            # For other states, provide a generic continuation
            return [{
                "verb": "gather",
                "actionHook": "websocket",
                "input": ["speech", "dtmf"],
                "timeout": 10,
                "bargein": True,
                "say": {
                    "text": "Please continue with your request or say 'main menu' to start over."
                }
            }]

# Global message handler instance
message_handler = JambonzMessageHandler()
