#!/usr/bin/env python3
"""
Test script to verify the WebSocket connection handler fix.
This tests that the _handle_connection method now works with the correct signature.
Updated to test the wrapper function fix for path parameter handling.
"""

import asyncio
import websockets
import json
import sys
import time

async def test_websocket_connection():
    """Test WebSocket connection to verify the fix"""
    
    # Test configuration
    hospital_id = "3854690459"
    websocket_url = f"ws://localhost:8765/ws/jambonz/{hospital_id}"
    
    print(f"Testing WebSocket connection to: {websocket_url}")
    print("=" * 60)
    
    try:
        # Connect to WebSocket server
        print("1. Attempting to connect...")
        async with websockets.connect(
            websocket_url,
            subprotocols=["ws.jambonz.org"],
            timeout=10
        ) as websocket:
            print("   ✅ WebSocket connection established successfully!")
            print(f"   Connected to: {websocket.remote_address}")
            print(f"   Subprotocol: {websocket.subprotocol}")
            
            # Test sending a session:new message
            print("\n2. Sending test message...")
            test_message = {
                "type": "session:new",
                "msgid": "test-001",
                "call_sid": "test-call-001",
                "from": "+1234567890",
                "to": "+9876543210",
                "hospital_id": hospital_id
            }
            
            await websocket.send(json.dumps(test_message))
            print("   ✅ Test message sent successfully!")
            
            # Wait for response
            print("\n3. Waiting for response...")
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=10)
                response_data = json.loads(response)
                print("   ✅ Received response:")
                print(f"   Response type: {response_data.get('type', 'unknown')}")
                print(f"   Message ID: {response_data.get('msgid', 'none')}")
                
                if response_data.get('verbs'):
                    print(f"   Verbs count: {len(response_data['verbs'])}")
                    
            except asyncio.TimeoutError:
                print("   ⚠️ No response received within timeout (this might be normal)")
            except Exception as e:
                print(f"   ❌ Error receiving response: {e}")
            
            print("\n4. Testing connection cleanup...")
            await websocket.close()
            print("   ✅ Connection closed successfully!")
            
    except ConnectionRefusedError:
        print("   ❌ Connection refused - WebSocket server not running")
        print("   Please start the server with: python -m uvicorn voice_agent.main:app --host 0.0.0.0 --port 8000")
        return False
        
    except websockets.exceptions.InvalidStatusCode as e:
        print(f"   ❌ Invalid status code: {e}")
        print("   This might indicate the server is not properly handling WebSocket upgrades")
        return False
        
    except Exception as e:
        print(f"   ❌ Connection error: {e}")
        return False
    
    return True

async def test_proxy_connection():
    """Test WebSocket proxy connection through FastAPI"""
    
    hospital_id = "3854690459"
    proxy_url = f"ws://localhost:8000/ws/jambonz/{hospital_id}"
    
    print(f"\nTesting WebSocket PROXY connection to: {proxy_url}")
    print("=" * 60)
    
    try:
        # Connect to proxy
        print("1. Attempting to connect to proxy...")
        async with websockets.connect(
            proxy_url,
            subprotocols=["ws.jambonz.org"],
            timeout=10
        ) as websocket:
            print("   ✅ WebSocket proxy connection established successfully!")
            
            # Test sending a message through proxy
            print("\n2. Sending test message through proxy...")
            test_message = {
                "type": "session:new",
                "msgid": "proxy-test-001",
                "call_sid": "proxy-call-001",
                "from": "+1234567890",
                "to": "+9876543210"
            }
            
            await websocket.send(json.dumps(test_message))
            print("   ✅ Test message sent through proxy!")
            
            # Wait for response
            print("\n3. Waiting for response through proxy...")
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=10)
                response_data = json.loads(response)
                print("   ✅ Received response through proxy:")
                print(f"   Response type: {response_data.get('type', 'unknown')}")
                
            except asyncio.TimeoutError:
                print("   ⚠️ No response received within timeout")
            except Exception as e:
                print(f"   ❌ Error receiving response: {e}")
            
            await websocket.close()
            print("   ✅ Proxy connection closed successfully!")
            
    except Exception as e:
        print(f"   ❌ Proxy connection error: {e}")
        return False
    
    return True

async def main():
    """Main test function"""
    print("WebSocket Connection Fix Test")
    print("This script tests both direct WebSocket and proxy connections")
    print()
    
    # Test direct WebSocket connection
    direct_success = await test_websocket_connection()
    
    # Small delay between tests
    await asyncio.sleep(2)
    
    # Test proxy connection
    proxy_success = await test_proxy_connection()
    
    print("\n" + "=" * 60)
    print("TEST SUMMARY:")
    print(f"Direct WebSocket: {'✅ PASSED' if direct_success else '❌ FAILED'}")
    print(f"Proxy WebSocket:  {'✅ PASSED' if proxy_success else '❌ FAILED'}")
    
    if direct_success and proxy_success:
        print("\n🎉 All tests passed! The WebSocket fix is working correctly.")
        print("Your Jambonz integration should now work properly.")
    elif direct_success:
        print("\n⚠️ Direct WebSocket works but proxy has issues.")
        print("Check the FastAPI server logs for proxy errors.")
    elif proxy_success:
        print("\n⚠️ Proxy works but direct WebSocket has issues.")
        print("Check the WebSocket server logs for connection errors.")
    else:
        print("\n❌ Both tests failed. Check server logs for errors.")
        print("Make sure both servers are running:")
        print("  python -m uvicorn voice_agent.main:app --host 0.0.0.0 --port 8000")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\nTest interrupted by user")
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        sys.exit(1)
